<template>
  <div class="oil-field-distribution-chart">
    <div class="chart-section">
      <div class="chart-box" ref="chartBox"></div>
    </div>
    <div class="table-section">
      <BorderTable
        :colums="tableColumns"
        :tableData="tableData"
        :showSummary="false"
        :cell-class-name="tableCellClassName"
        style="width: 100%"
        fit
      >
        <!-- 自定义单元格内容，为负值添加红色样式 -->
        <template v-slot:oilFieldCenter="{ scope }">
          <span :class="{ 'summary-row': scope.row.oilFieldCenter === '合计' }">
            {{ scope.row.oilFieldCenter }}
          </span>
        </template>
        <template v-slot:preTaxCost="{ scope }">
          <span :class="{ 'negative-value': scope.row.preTaxCost && parseFloat(scope.row.preTaxCost) < 0 }">
            {{ scope.row.preTaxCost }}
          </span>
        </template>
        <template v-slot:preTaxProfit="{ scope }">
          <span :class="{ 'negative-value': scope.row.preTaxProfit && parseFloat(scope.row.preTaxProfit) < 0 }">
            {{ scope.row.preTaxProfit }}
          </span>
        </template>
        <template v-slot:actualPrice="{ scope }">
          <span :class="{ 'negative-value': scope.row.actualPrice && parseFloat(scope.row.actualPrice) < 0 }">
            {{ scope.row.actualPrice }}
          </span>
        </template>
      </BorderTable>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import BorderTable from "@/components/comTable/borderTable.vue";

export default {
  name: "OilFieldDistributionChart",
  components: {
    BorderTable,
  },
  data() {
    return {
      chart: null,
      // 参考SalesChart的堆叠柱状图颜色方案
      stackColors: ["#3FB4FF", "#fb9352"],
      chartData: {
        // X轴分类数据 - 根据参考图片的油气田名称
        categories: ['气田平均', 'LS17-2', 'LS25-1', 'YC13-1', 'YC13-10', 'WC16-2'],
        // 数据系列 - 根据参考图片的数值
        seriesData: [
          {
            name: '税前桶油当量成本',
            data: [19.96, 11.82, 12.87, 15.12, 60.00, 0], // 蓝色部分数值
            stack: 'total'
          },
          {
            name: '税前桶油当量利润',
            data: [11.37, 39.68, 24.45, 22.71, -30.00, 0], // 橙色部分数值
            stack: 'total'
          }
        ]
      },
      // 表格列配置 - 根据参考图片的表格结构
      tableColumns: [
        {
          label: "利润中心",
          prop: "oilFieldCenter",
          align: "center",
          minWidth: 100,
          slotName: "oilFieldCenter"
        },
        {
          label: "税前桶油当量成本",
          prop: "preTaxCost",
          align: "center",
          minWidth: 120,
          slotName: "preTaxCost"
        },
        {
          label: "税前桶油当量利润",
          prop: "preTaxProfit",
          align: "center",
          minWidth: 120,
          slotName: "preTaxProfit"
        },
        {
          label: "实现价格",
          prop: "actualPrice",
          align: "center",
          minWidth: 100,
          slotName: "actualPrice"
        }
      ],
      // 表格数据 - 根据参考图片的具体数值
      tableData: [
        {
          oilFieldCenter: "气田平均",
          preTaxCost: "19.96",
          preTaxProfit: "11.37",
          actualPrice: "31.33"
        },
        {
          oilFieldCenter: "LS17-2",
          preTaxCost: "11.82",
          preTaxProfit: "39.68",
          actualPrice: "51.50"
        },
        {
          oilFieldCenter: "LS25-1",
          preTaxCost: "12.87",
          preTaxProfit: "24.45",
          actualPrice: "37.32"
        },
        {
          oilFieldCenter: "YC13-1",
          preTaxCost: "15.12",
          preTaxProfit: "22.71",
          actualPrice: "37.83"
        },
        {
          oilFieldCenter: "YC13-10",
          preTaxCost: "60.00",
          preTaxProfit: "-30.00",
          actualPrice: "30.00"
        },
        {
          oilFieldCenter: "WC16-2",
          preTaxCost: "0.00",
          preTaxProfit: "0.00",
          actualPrice: "0.00"
        }
      ]
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    // 监听窗口大小变化，实现响应式
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 清理事件监听器和图表实例
    window.removeEventListener('resize', this.handleResize);
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    initChart() {
      // 如果已有实例，先销毁
      if (this.chart) {
        this.chart.dispose();
      }

      this.chart = echarts.init(this.$refs.chartBox);

      const option = {
        backgroundColor: "transparent",
        legend: {
          data: ["税前桶油当量成本", "税前桶油当量利润"],
          top: "5%",
          right: "center",
          orient: "horizontal",
          textStyle: {
            color: "#ACC2E2",
            fontSize: 12,
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 20,
          icon: "rect",
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;z-index: 9999;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: this.tooltipFormatter,
        },
        grid: {
          top: "15%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartData.categories,
            axisTick: {
              alignWithLabel: true,
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "rgba(172, 194, 226, 0.2)",
              },
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
                fontSize: 12,
              },
              interval: 0,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "right",
              padding: [0, 10, 0, 0],
            },
            axisLabel: {
              textStyle: {
                color: "#ACC2E2",
              },
              formatter: "{value}",
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(172, 194, 226, 0.1)",
                type: "dashed",
              },
            },
            axisLine: {
              show: false,
            },
          },
        ],
        series: this.chartData.seriesData.map((item, index) => ({
          name: item.name,
          type: 'bar',
          stack: item.stack,
          data: item.data,
          itemStyle: {
            color: this.stackColors[index]
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (params) => {
              // 只显示非零值
              return params.value !== 0 ? params.value.toFixed(2) : '';
            },
            fontSize: 12,
            color: '#FFFFFF',
            fontWeight: 'bold'
          },
          barWidth: '60%'
        }))
      };

      // 设置图表配置
      this.chart.setOption(option);
    },

    tooltipFormatter(params) {
      if (!params || params.length === 0) return '';
      
      const dataIndex = params[0].dataIndex;
      const categoryName = this.chartData.categories[dataIndex];
      
      let content = `<div style="margin-bottom: 8px; font-weight: bold;">${categoryName}</div>`;
      
      params.forEach(param => {
        const color = param.color;
        const seriesName = param.seriesName;
        const value = param.value;
        
        if (value !== 0) {
          content += `<div style="display: flex; align-items: center; margin-bottom: 4px;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
            <span>${seriesName}: $${value.toFixed(2)}/BOE</span>
          </div>`;
        }
      });
      
      return content;
    },

    handleResize() {
      // 响应式处理
      if (this.chart) {
        this.chart.resize();
      }
    },

    // 更新图表数据的方法
    updateChartData(newData) {
      if (this.chart && newData) {
        this.chartData = { ...this.chartData, ...newData };
        this.initChart();
      }
    },

    // 更新表格数据的方法
    updateTableData(newTableData) {
      if (newTableData) {
        this.tableData = newTableData;
      }
    },

    // 同步更新图表和表格数据
    updateAllData(newChartData, newTableData) {
      if (newChartData) {
        this.updateChartData(newChartData);
      }
      if (newTableData) {
        this.updateTableData(newTableData);
      }
    },

    // 表格单元格样式类名
    tableCellClassName() {
      return '';
    }
  }
};
</script>

<style lang="scss" scoped>
.oil-field-distribution-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .chart-section {
    flex: 1;
    min-height: 200px;
    max-height: 280px;

    .chart-box {
      width: 100%;
      height: 100%;
    }
  }

  .table-section {
    flex-shrink: 0;
    width: 100%;
    padding: 16px;
  }
}

// 保留基本的表格宽度设置
:deep(.border-table) {
  width: 100%;

  .el-table {
    width: 100% !important;
  }
}

// 负值样式
.negative-value {
  color: #ff6b6b !important;
  font-weight: bold;
}

// 合计行文字样式
.summary-row {
  font-weight: bold;
}
</style>
