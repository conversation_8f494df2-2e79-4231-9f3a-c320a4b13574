<template>
  <div class="budget-analysis">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <CarouselBtn :buttons="buttons1" />
        </chartBox>
      </div>
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <CarouselBtn :buttons="buttons2"
        /></chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="cost-structure">
        <chartBox :title="'成本结构'">
          <CarouselBtn :buttons="buttons3" />
        </chartBox>
      </div>
      <div class="historical-trend">
        <chartBox :title="'历史趋势'">
          <CarouselBtn :buttons="buttons4" />
        </chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CarouselBtn from "../components/CarouselBtn.vue";
export default {
  name: "BudgetAnalysis",
  components: {
    CarouselBtn,
  },
  data() {
    return {
      buttons1: [
        "油气收入",
        "总产量",
        "净产量",
        "油气均价",
        "税前利润",
        "经营现金流",
      ],
      buttons2:["完全支出","完全成本","桶油五项","OPEX","DD&A","SG&A"],
      buttons3:["完全成本","五项成本","OPEX费用","管理费用","销售费用"],
      buttons4:["总产量","净销量","油气均价","桶油五项","桶油OPEX"]
    };
  },
};
</script>
<style lang="scss" scoped></style>
