<template>
  <div class="budget-analysis">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览1'">
          <CarouselBtn :buttons="buttons1" @clickHandle="handleButton1Click" />
          <MainIndicatorsChart :activeIndex="activeButton1Index" />
        </chartBox>
      </div>
      <div class="main-indicators">
        <chartBox :title="'主要指标总览2'">
          <CarouselBtn :buttons="buttons2" />
        </chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="cost-structure">
        <chartBox :title="'成本结构'">
          <CarouselBtn :buttons="buttons3" />
        </chartBox>
      </div>
      <div class="historical-trend">
        <chartBox :title="'历史趋势'">
          <CarouselBtn :buttons="buttons4" />
        </chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CarouselBtn from "../components/CarouselBtn.vue";
import MainIndicatorsChart from "./mainIndicatorsChart/index.vue";

export default {
  name: "BudgetAnalysis",
  components: {
    CarouselBtn,
    MainIndicatorsChart,
  },
  data() {
    return {
      activeButton1Index: 0,
      buttons1: [
        "油气收入",
        "总产量",
        "净产量",
        "油气均价",
        "税前利润",
        "经营现金流",
      ],
      buttons2:["完全支出","完全成本","桶油五项","OPEX","DD&A","SG&A"],
      buttons3:["完全成本","五项成本","OPEX费用","管理费用","销售费用"],
      buttons4:["总产量","净销量","油气均价","桶油五项","桶油OPEX"]
    };
  },
  methods: {
    handleButton1Click(index) {
      this.activeButton1Index = index;
    }
  }
};
</script>
<style lang="scss" scoped>
.budget-analysis {
  .content-up {
    display: flex;
    justify-content: space-between;
    gap: 10px;

    .main-indicators {
      flex: 1;
      min-width: 0;
    }
  }

  .content-down {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    gap: 10px;

    .cost-structure,
    .historical-trend {
      flex: 1;
      min-width: 0;
    }
  }
}
</style>
