<template>
  <div class="net-sales-distribution-chart">
    <div class="chart-section">
      <div class="chart-box" ref="chartBox"></div>
    </div>
    <div class="table-section">
      <BorderTable
        :colums="tableColumns"
        :tableData="tableData"
        :showSummary="false"
        :cell-class-name="tableCellClassName"
        style="width: 100%"
        fit
      >
        <!-- 自定义单元格内容，为负值添加红色样式 -->
        <template v-slot:profitLevel="{ scope }">
          <span :class="{
            'negative-value': scope.row.profitLevel === '<0',
            'summary-row': scope.row.profitLevel === '合计'
          }">
            {{ scope.row.profitLevel }}
          </span>
        </template>
        <template v-slot:yc13_10="{ scope }">
          <span :class="{ 'negative-value': scope.row.yc13_10 && parseFloat(scope.row.yc13_10) < 0 }">
            {{ scope.row.yc13_10 }}
          </span>
        </template>
        <template v-slot:total="{ scope }">
          <span :class="{ 'negative-value': scope.row.total && parseFloat(scope.row.total) < 0 }">
            {{ scope.row.total }}
          </span>
        </template>
        <template v-slot:percentage="{ scope }">
          <span :class="{ 'negative-value': scope.row.percentage && parseFloat(scope.row.percentage) < 0 }">
            {{ scope.row.percentage }}
          </span>
        </template>
      </BorderTable>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import BorderTable from "@/components/comTable/borderTable.vue";

export default {
  name: "NetSalesDistributionChart",
  components: {
    BorderTable,
  },
  data() {
    return {
      chart: null,
      // 根据图片中的数据和颜色配置
      chartData: [
        {
          name: '>60',
          value: 60.1,
          itemStyle: {
            color: '#90C695' // 绿色
          }
        },
        {
          name: '40-60',
          value: 38.1,
          itemStyle: {
            color: '#7BB3E8' // 蓝色
          }
        },
        {
          name: '0-40',
          value: 1.8,
          itemStyle: {
            color: '#B8D4A8' // 浅绿色
          }
        },
        {
          name: '<0',
          value: 0.0,
          itemStyle: {
            color: '#A8A8A8' // 灰色
          }
        }
      ],
      // 表格列配置 - 优化列宽分配
      tableColumns: [
        {
          label: "税前桶油盈利水平($/Boe)",
          prop: "profitLevel",
          align: "center",
          minWidth: 160,
          slotName: "profitLevel"
        },
        {
          label: "净销量(万桶)",
          children: [
            {
              label: "LS17-2",
              prop: "ls17_2",
              align: "center",
              minWidth: 80
            },
            {
              label: "LS25-1",
              prop: "ls25_1",
              align: "center",
              minWidth: 80
            },
            {
              label: "YC13-1",
              prop: "yc13_1",
              align: "center",
              minWidth: 80
            },
            {
              label: "YC13-10",
              prop: "yc13_10",
              align: "center",
              minWidth: 80,
              slotName: "yc13_10"
            },
            {
              label: "WC16-2",
              prop: "wc16_2",
              align: "center",
              minWidth: 80
            },
            {
              label: "合计",
              prop: "total",
              align: "center",
              minWidth: 80,
              slotName: "total"
            },
            {
              label: "占比",
              prop: "percentage",
              align: "center",
              minWidth: 80,
              slotName: "percentage"
            }
          ]
        }
      ],
      // 表格数据 - 根据图片中的具体数值
      tableData: [
        {
          profitLevel: ">60",
          ls17_2: "231.34",
          ls25_1: "",
          yc13_1: "",
          yc13_10: "",
          wc16_2: "",
          total: "231.34",
          percentage: "82.13%"
        },
        {
          profitLevel: "40-60",
          ls17_2: "",
          ls25_1: "26.70",
          yc13_1: "16.70",
          yc13_10: "",
          wc16_2: "",
          total: "43.41",
          percentage: "15.41%"
        },
        {
          profitLevel: "0-40",
          ls17_2: "",
          ls25_1: "",
          yc13_1: "",
          yc13_10: "",
          wc16_2: "",
          total: "0.00",
          percentage: "0.00%"
        },
        {
          profitLevel: "<0",
          ls17_2: "",
          ls25_1: "",
          yc13_1: "",
          yc13_10: "6.93",
          wc16_2: "",
          total: "6.93",
          percentage: "2.46%"
        },
        // 合计行
        {
          profitLevel: "合计",
          ls17_2: "231.34",
          ls25_1: "26.7",
          yc13_1: "16.7",
          yc13_10: "6.93",
          wc16_2: "0",
          total: "281.68",
          percentage: ""
        }
      ]
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    // 监听窗口大小变化，实现响应式
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 清理事件监听器和图表实例
    window.removeEventListener('resize', this.handleResize);
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    initChart() {
      // 如果已有实例，先销毁
      if (this.chart) {
        this.chart.dispose();
      }

      this.chart = echarts.init(this.$refs.chartBox);

      const option = {
        backgroundColor: "transparent",
        tooltip: {
          trigger: 'item',
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;z-index: 9999;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          formatter: (params) => {
            return `<div style="margin-bottom: 8px; font-weight: bold;">${params.name}</div>
                    <div style="display: flex; align-items: center;">
                      <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
                      <span>占比: ${params.percent}%</span>
                    </div>`;
          }
        },
        legend: {
          orient: 'vertical',
          top: 'center',
          textStyle: {
            color: "#ACC2E2",
            fontSize: 12,
          },
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 15,
          icon: "rect",
          formatter: (name) => {
            return name;
          }
        },
        series: [
          {
            name: '净销量区间分布',
            type: 'pie',
            radius: ['0%', '65%'],
            center: ['38%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'inside',
              formatter: (params) => {
                // 只显示占比大于1%的标签
                if (params.percent > 1) {
                  return `${params.percent}%`;
                }
                return '';
              },
              fontSize: 12,
              color: '#FFFFFF',
              fontWeight: 'bold'
            },
            labelLine: {
              show: false
            },
            data: this.chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(255, 255, 255, 0.3)'
              }
            }
          }
        ]
      };

      // 设置图表配置
      this.chart.setOption(option);
    },

    handleResize() {
      // 响应式处理
      if (this.chart) {
        this.chart.resize();
      }
    },

    // 更新图表数据的方法
    updateChartData(newData) {
      if (this.chart && newData) {
        this.chartData = newData;
        this.initChart();
      }
    },

    // 更新表格数据的方法
    updateTableData(newTableData) {
      if (newTableData) {
        this.tableData = newTableData;
      }
    },

    // 同步更新图表和表格数据
    updateAllData(newChartData, newTableData) {
      if (newChartData) {
        this.updateChartData(newChartData);
      }
      if (newTableData) {
        this.updateTableData(newTableData);
      }
    },

    // 表格单元格样式类名 - 简化版本，移除自定义背景色
    tableCellClassName() {
      // 移除所有自定义样式，使用BorderTable组件的默认样式
      return '';
    }
  }
};
</script>

<style lang="scss" scoped>
.net-sales-distribution-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .chart-section {
    flex: 1;
    min-height: 200px;
    max-height: 280px;

    .chart-box {
      width: 100%;
      height: 100%;
    }
  }

  .table-section {
    flex-shrink: 0;
    width: 100%;
    padding: 16px;
  }
}

// 保留基本的表格宽度设置，移除自定义颜色样式
:deep(.border-table) {
  width: 100%;

  .el-table {
    width: 100% !important;
  }
}

// 负值样式
.negative-value {
  color: #ff6b6b !important;
  font-weight: bold;
}

// 合计行文字样式
.summary-row {
  font-weight: bold;
}

// ::v-deep .el-table--border::after{
//   width: 0;
// }
</style>
