<template>
  <div class="main-indicators-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "MainIndicatorsChart",
  props: {
    activeIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      myChart: null,
      chartDataMap: {
        0: { // 油气收入
          title: "油气收入执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [95, 85, 75, 0, 0], // 欠执行百分比
          overPerform: [0, 0, 0, 45, 35] // 超执行百分比
        },
        1: { // 总产量
          title: "总产量执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [88, 92, 78, 0, 0],
          overPerform: [0, 0, 0, 52, 38]
        },
        2: { // 净产量
          title: "净产量执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [90, 87, 82, 0, 0],
          overPerform: [0, 0, 0, 48, 42]
        },
        3: { // 油气均价
          title: "油气均价执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [85, 90, 70, 0, 0],
          overPerform: [0, 0, 0, 55, 40]
        },
        4: { // 税前利润
          title: "税前利润执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [92, 88, 76, 0, 0],
          overPerform: [0, 0, 0, 50, 45]
        },
        5: { // 经营现金流
          title: "经营现金流执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [89, 91, 73, 0, 0],
          overPerform: [0, 0, 0, 47, 41]
        }
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  watch: {
    activeIndex: {
      handler() {
        this.updateChart();
      },
      immediate: true
    }
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }
      this.myChart = echarts.init(this.$refs.chartBox);
      this.updateChart();
    },
    updateChart() {
      if (!this.myChart) return;
      
      const currentData = this.chartDataMap[this.activeIndex] || this.chartDataMap[0];
      
      const option = {
        color: ["#FF8C42", "#248EFF"], // 橙色和蓝色，与BreakEven页面风格一致
        legend: {
          data: ["欠", "超"],
          top: "5%",
          right: "center",
          orient: "horizontal",
          textStyle: {
            color: "#ACC2E2",
            fontSize: 12,
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 20,
          icon: "rect",
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;z-index: 9999;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 14,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          formatter: (params) => {
            let result = `<div style="margin-bottom: 8px; font-weight: bold;">${params[0].name}</div>`;
            params.forEach(param => {
              if (param.value > 0) {
                result += `<div style="display: flex; align-items: center; margin-bottom: 4px;">
                  <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; margin-right: 8px;"></span>
                  <span>${param.seriesName}: ${param.value}%</span>
                </div>`;
              }
            });
            return result;
          }
        },
        grid: {
          top: "15%",
          left: "8%",
          right: "15%",
          bottom: "5%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          min: 0,
          max: 120,
          interval: 20,
          axisLabel: {
            color: "#ACC2E2",
            fontSize: 10,
            formatter: "{value}%"
          },
          axisLine: {
            lineStyle: {
              color: "#2A3A5C"
            }
          },
          splitLine: {
            lineStyle: {
              color: "#2A3A5C",
              type: "dashed"
            }
          }
        },
        yAxis: {
          type: "category",
          data: currentData.fields,
          axisLabel: {
            color: "#ACC2E2",
            fontSize: 11
          },
          axisLine: {
            lineStyle: {
              color: "#2A3A5C"
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: "欠",
            type: "bar",
            stack: "total",
            barWidth: "60%",
            itemStyle: {
              borderRadius: [4, 0, 0, 4]
            },
            data: currentData.underPerform,
            label: {
              show: true,
              position: "insideRight",
              color: "#FFFFFF",
              fontSize: 10,
              formatter: (params) => {
                return params.value > 0 ? `${params.value}%` : '';
              }
            }
          },
          {
            name: "超",
            type: "bar",
            stack: "total",
            barWidth: "60%",
            itemStyle: {
              borderRadius: [0, 4, 4, 0]
            },
            data: currentData.overPerform,
            label: {
              show: true,
              position: "insideRight",
              color: "#FFFFFF",
              fontSize: 10,
              formatter: (params) => {
                return params.value > 0 ? `${params.value}%` : '';
              }
            }
          }
        ]
      };

      this.myChart.setOption(option, true);
    },
    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.main-indicators-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .chart-box {
    width: 100%;
    flex: 1;
    min-height: 300px;
    max-height: 400px;
  }
}
</style>
