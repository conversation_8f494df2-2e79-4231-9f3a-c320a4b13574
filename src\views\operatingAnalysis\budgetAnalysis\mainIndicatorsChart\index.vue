<template>
  <div class="main-indicators-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "MainIndicatorsChart",
  props: {
    activeIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      myChart: null,
      chartDataMap: {
        0: { // 油气收入
          title: "油气收入执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [85, 75, 65, 0, 0], // 欠执行百分比，更接近图片效果
          overPerform: [0, 0, 0, 50, 35] // 超执行百分比
        },
        1: { // 总产量
          title: "总产量执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [88, 78, 68, 0, 0],
          overPerform: [0, 0, 0, 48, 32]
        },
        2: { // 净产量
          title: "净产量执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [82, 72, 62, 0, 0],
          overPerform: [0, 0, 0, 45, 38]
        },
        3: { // 油气均价
          title: "油气均价执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [90, 80, 70, 0, 0],
          overPerform: [0, 0, 0, 52, 40]
        },
        4: { // 税前利润
          title: "税前利润执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [86, 76, 66, 0, 0],
          overPerform: [0, 0, 0, 46, 36]
        },
        5: { // 经营现金流
          title: "经营现金流执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [84, 74, 64, 0, 0],
          overPerform: [0, 0, 0, 44, 34]
        }
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  watch: {
    activeIndex: {
      handler() {
        this.updateChart();
      },
      immediate: true
    }
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }
      this.myChart = echarts.init(this.$refs.chartBox);
      this.updateChart();
    },
    updateChart() {
      if (!this.myChart) return;
      
      const currentData = this.chartDataMap[this.activeIndex] || this.chartDataMap[0];
      
      const option = {
        color: ["#FF8B42", "#4A90E2"], // 更精确匹配图片中的橙色和蓝色
        legend: {
          data: ["欠", "超"],
          top: "3%",
          left: "center",
          orient: "horizontal",
          textStyle: {
            color: "#E6F1FF",
            fontSize: 14,
            fontWeight: "normal"
          },
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 30,
          icon: "rect",
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(15, 25, 55, 0.95)",
          extraCssText: "box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.6);border-radius: 6px;z-index: 9999;",
          textStyle: {
            fontFamily: "Microsoft YaHei",
            color: "#FFFFFF",
            fontSize: 13,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.08)'
            }
          },
          formatter: (params) => {
            let result = `<div style="margin-bottom: 6px; font-weight: bold; color: #E6F1FF;">${params[0].name}</div>`;
            params.forEach(param => {
              if (param.value > 0) {
                result += `<div style="display: flex; align-items: center; margin-bottom: 3px;">
                  <span style="display: inline-block; width: 8px; height: 8px; background-color: ${param.color}; margin-right: 6px; border-radius: 1px;"></span>
                  <span style="color: #FFFFFF;">${param.seriesName}: ${param.value}%</span>
                </div>`;
              }
            });
            return result;
          }
        },
        grid: {
          top: "18%",
          left: "12%",
          right: "8%",
          bottom: "8%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          min: 0,
          max: 120,
          interval: 20,
          axisLabel: {
            color: "#8FA4C7",
            fontSize: 11,
            fontFamily: "Microsoft YaHei",
            formatter: "{value}%",
            margin: 8
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#2D4A6B",
              type: "solid",
              width: 1,
              opacity: 0.6
            }
          }
        },
        yAxis: {
          type: "category",
          data: currentData.fields,
          axisLabel: {
            color: "#E6F1FF",
            fontSize: 13,
            fontFamily: "Microsoft YaHei",
            fontWeight: "normal",
            margin: 15,
            align: "right"
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            name: "欠",
            type: "bar",
            stack: "total",
            barWidth: "45%",
            itemStyle: {
              borderRadius: [4, 0, 0, 4],
              shadowColor: 'rgba(255, 139, 66, 0.4)',
              shadowBlur: 6,
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            data: currentData.underPerform,
            label: {
              show: false
            }
          },
          {
            name: "超",
            type: "bar",
            stack: "total",
            barWidth: "45%",
            itemStyle: {
              borderRadius: [0, 4, 4, 0],
              shadowColor: 'rgba(74, 144, 226, 0.4)',
              shadowBlur: 6,
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            data: currentData.overPerform,
            label: {
              show: false
            }
          }
        ]
      };

      this.myChart.setOption(option, true);
    },
    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.main-indicators-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;

  .chart-box {
    width: 100%;
    flex: 1;
    min-height: 280px;
    max-height: 350px;
    padding: 8px 0;
  }
}
</style>
