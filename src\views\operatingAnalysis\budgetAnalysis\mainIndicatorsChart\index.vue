<template>
  <div class="main-indicators-chart">
    <!-- 自定义图例 -->
    <div class="custom-legend">
      <div class="legend-item">
        <span class="legend-color" style="background-color: #FF8B42;"></span>
        <span class="legend-text">欠</span>
      </div>
      <div class="legend-item">
        <span class="legend-color" style="background-color: #4A90E2;"></span>
        <span class="legend-text">超</span>
      </div>
    </div>
    <!-- 双图表容器 -->
    <div class="charts-container">
      <div class="left-chart" ref="leftChartBox"></div>
      <div class="right-chart" ref="rightChartBox"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "MainIndicatorsChart",
  props: {
    activeIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      leftChart: null,
      rightChart: null,
      chartDataMap: {
        0: { // 油气收入
          title: "油气收入执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [85, 75, 65, 0, 0], // 欠执行数据
          overPerform: [0, 0, 0, 50, 35] // 超执行数据
        },
        1: { // 总产量
          title: "总产量执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [88, 78, 68, 0, 0],
          overPerform: [0, 0, 0, 48, 32]
        },
        2: { // 净产量
          title: "净产量执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [82, 72, 62, 0, 0],
          overPerform: [0, 0, 0, 45, 38]
        },
        3: { // 油气均价
          title: "油气均价执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [90, 80, 70, 0, 0],
          overPerform: [0, 0, 0, 52, 40]
        },
        4: { // 税前利润
          title: "税前利润执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [86, 76, 66, 0, 0],
          overPerform: [0, 0, 0, 46, 36]
        },
        5: { // 经营现金流
          title: "经营现金流执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          underPerform: [84, 74, 64, 0, 0],
          overPerform: [0, 0, 0, 44, 34]
        }
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts();
    });
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.leftChart) {
      this.leftChart.dispose();
    }
    if (this.rightChart) {
      this.rightChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  watch: {
    activeIndex: {
      handler() {
        this.updateCharts();
      },
      immediate: true
    }
  },
  methods: {
    initCharts() {
      if (this.leftChart) {
        this.leftChart.dispose();
      }
      if (this.rightChart) {
        this.rightChart.dispose();
      }
      this.leftChart = echarts.init(this.$refs.leftChartBox);
      this.rightChart = echarts.init(this.$refs.rightChartBox);
      this.updateCharts();
    },
    updateCharts() {
      if (!this.leftChart || !this.rightChart) return;

      const currentData = this.chartDataMap[this.activeIndex] || this.chartDataMap[0];

      // 左侧图表配置（欠执行）
      const leftOption = {
        grid: {
          top: "8%",
          left: "8%",
          right: "12%", // 适度增加右边距，为Y轴标签预留空间
          bottom: "15%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(15, 25, 55, 0.95)",
          extraCssText: "box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.6);border-radius: 6px;z-index: 9999;",
          textStyle: {
            fontFamily: "Microsoft YaHei",
            color: "#FFFFFF",
            fontSize: 13,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.08)'
            }
          },
          formatter: (params) => {
            const param = params[0];
            const value = param.value;

            let result = `<div style="margin-bottom: 6px; font-weight: bold; color: #E6F1FF;">${param.name}</div>`;
            if (value > 0) {
              result += `<div style="display: flex; align-items: center; margin-bottom: 3px;">
                <span style="display: inline-block; width: 8px; height: 8px; background-color: #FF8B42; margin-right: 6px; border-radius: 1px;"></span>
                <span style="color: #FFFFFF;">欠: ${value}%</span>
              </div>`;
            }
            return result;
          }
        },
        xAxis: {
          type: "value",
          min: 0,
          max: 120,
          interval: 20,
          inverse: true, // 反向显示，从右到左
          position: 'bottom',
          axisLabel: {
            color: "#8FA4C7",
            fontSize: 11,
            fontFamily: "Microsoft YaHei",
            formatter: "{value}%",
            margin: 8
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#2D4A6B",
              type: "solid",
              width: 1,
              opacity: 0.6
            }
          }
        },
        yAxis: {
          type: "category",
          data: currentData.fields,
          axisLabel: {
            show: false // 左侧图表不显示Y轴标签
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            name: "欠",
            type: "bar",
            barWidth: "50%",
            itemStyle: {
              color: "#FF8B42",
              borderRadius: [4, 0, 0, 4],
              shadowColor: 'rgba(255, 139, 66, 0.4)',
              shadowBlur: 6,
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            data: currentData.underPerform,
            label: {
              show: false
            }
          }
        ]
      };

      // 右侧图表配置（超执行）
      const rightOption = {
        grid: {
          top: "8%",
          left: "12%", // 适度增加左边距，为Y轴标签预留空间
          right: "8%",
          bottom: "15%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(15, 25, 55, 0.95)",
          extraCssText: "box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.6);border-radius: 6px;z-index: 9999;",
          textStyle: {
            fontFamily: "Microsoft YaHei",
            color: "#FFFFFF",
            fontSize: 13,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.08)'
            }
          },
          formatter: (params) => {
            const param = params[0];
            const value = param.value;

            let result = `<div style="margin-bottom: 6px; font-weight: bold; color: #E6F1FF;">${param.name}</div>`;
            if (value > 0) {
              result += `<div style="display: flex; align-items: center; margin-bottom: 3px;">
                <span style="display: inline-block; width: 8px; height: 8px; background-color: #4A90E2; margin-right: 6px; border-radius: 1px;"></span>
                <span style="color: #FFFFFF;">超: ${value}%</span>
              </div>`;
            }
            return result;
          }
        },
        xAxis: {
          type: "value",
          min: 0,
          max: 120,
          interval: 20,
          position: 'bottom',
          axisLabel: {
            color: "#8FA4C7",
            fontSize: 11,
            fontFamily: "Microsoft YaHei",
            formatter: "{value}%",
            margin: 8
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#2D4A6B",
              type: "solid",
              width: 1,
              opacity: 0.6
            }
          }
        },
        yAxis: {
          type: "category",
          data: currentData.fields,
          axisLabel: {
            color: "#E6F1FF",
            fontSize: 13,
            fontFamily: "Microsoft YaHei",
            fontWeight: "normal",
            margin: 15,
            align: "center" // 居中对齐，使标签显示在左右图表中间
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            name: "超",
            type: "bar",
            barWidth: "50%",
            itemStyle: {
              color: "#4A90E2",
              borderRadius: [0, 4, 4, 0],
              shadowColor: 'rgba(74, 144, 226, 0.4)',
              shadowBlur: 6,
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            data: currentData.overPerform,
            label: {
              show: false
            }
          }
        ]
      };

      this.leftChart.setOption(leftOption, true);
      this.rightChart.setOption(rightOption, true);
    },
    handleResize() {
      if (this.leftChart) {
        this.leftChart.resize();
      }
      if (this.rightChart) {
        this.rightChart.resize();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.main-indicators-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;

  .custom-legend {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    padding: 10px 0;
    margin-bottom: 10px;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
      }

      .legend-text {
        color: #E6F1FF;
        font-size: 14px;
        font-family: "Microsoft YaHei";
        font-weight: normal;
      }
    }
  }

  .charts-container {
    display: flex;
    flex: 1;
    min-height: 280px;
    max-height: 350px;

    .left-chart {
      width: 50%;
      height: 100%;
    }

    .right-chart {
      width: 50%;
      height: 100%;
    }
  }
}
</style>
