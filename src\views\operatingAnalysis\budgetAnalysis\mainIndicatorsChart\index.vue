<template>
  <div class="main-indicators-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "MainIndicatorsChart",
  props: {
    activeIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      myChart: null,
      chartDataMap: {
        0: { // 油气收入
          title: "油气收入执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          // 双向数据：负值表示"欠"，正值表示"超"
          data: [-85, -75, -65, 50, 35]
        },
        1: { // 总产量
          title: "总产量执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          data: [-88, -78, -68, 48, 32]
        },
        2: { // 净产量
          title: "净产量执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          data: [-82, -72, -62, 45, 38]
        },
        3: { // 油气均价
          title: "油气均价执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          data: [-90, -80, -70, 52, 40]
        },
        4: { // 税前利润
          title: "税前利润执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          data: [-86, -76, -66, 46, 36]
        },
        5: { // 经营现金流
          title: "经营现金流执行情况",
          fields: ["LS25-1", "YC13-1", "LS17-2", "YC13-4", "YC13-10"],
          data: [-84, -74, -64, 44, 34]
        }
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  watch: {
    activeIndex: {
      handler() {
        this.updateChart();
      },
      immediate: true
    }
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }
      this.myChart = echarts.init(this.$refs.chartBox);
      this.updateChart();
    },
    updateChart() {
      if (!this.myChart) return;
      
      const currentData = this.chartDataMap[this.activeIndex] || this.chartDataMap[0];
      
      const option = {
        color: ["#FF8B42", "#4A90E2"], // 更精确匹配图片中的橙色和蓝色
        legend: {
          show: false // 暂时隐藏图例，因为双向条形图的图例处理比较复杂
        },
        // 自定义图例
        graphic: [
          {
            type: 'group',
            left: 'center',
            top: '3%',
            children: [
              {
                type: 'rect',
                shape: {
                  x: -50,
                  y: 0,
                  width: 12,
                  height: 12
                },
                style: {
                  fill: '#FF8B42'
                }
              },
              {
                type: 'text',
                style: {
                  text: '欠',
                  x: -35,
                  y: 10,
                  textAlign: 'left',
                  textVerticalAlign: 'middle',
                  fill: '#E6F1FF',
                  fontSize: 14,
                  fontFamily: 'Microsoft YaHei'
                }
              },
              {
                type: 'rect',
                shape: {
                  x: 10,
                  y: 0,
                  width: 12,
                  height: 12
                },
                style: {
                  fill: '#4A90E2'
                }
              },
              {
                type: 'text',
                style: {
                  text: '超',
                  x: 25,
                  y: 10,
                  textAlign: 'left',
                  textVerticalAlign: 'middle',
                  fill: '#E6F1FF',
                  fontSize: 14,
                  fontFamily: 'Microsoft YaHei'
                }
              }
            ]
          }
        ],
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(15, 25, 55, 0.95)",
          extraCssText: "box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.6);border-radius: 6px;z-index: 9999;",
          textStyle: {
            fontFamily: "Microsoft YaHei",
            color: "#FFFFFF",
            fontSize: 13,
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(255, 255, 255, 0.08)'
            }
          },
          formatter: (params) => {
            const param = params[0];
            const value = param.value;
            const absValue = Math.abs(value);
            const type = value < 0 ? '欠' : '超';
            const color = value < 0 ? '#FF8B42' : '#4A90E2';

            let result = `<div style="margin-bottom: 6px; font-weight: bold; color: #E6F1FF;">${param.name}</div>`;
            if (value !== 0) {
              result += `<div style="display: flex; align-items: center; margin-bottom: 3px;">
                <span style="display: inline-block; width: 8px; height: 8px; background-color: ${color}; margin-right: 6px; border-radius: 1px;"></span>
                <span style="color: #FFFFFF;">${type}: ${absValue}%</span>
              </div>`;
            }
            return result;
          }
        },
        grid: {
          top: "20%",
          left: "12%",
          right: "8%",
          bottom: "8%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          min: -120,
          max: 120,
          interval: 20,
          axisLabel: {
            color: "#8FA4C7",
            fontSize: 11,
            fontFamily: "Microsoft YaHei",
            formatter: function(value) {
              // 显示绝对值并添加%符号
              return Math.abs(value) + '%';
            },
            margin: 8
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#2D4A6B",
              width: 2
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: function(params) {
                // 0轴线更明显
                return params.value === 0 ? "#4A6B8A" : "#2D4A6B";
              },
              type: "solid",
              width: function(params) {
                return params.value === 0 ? 2 : 1;
              },
              opacity: 0.8
            }
          }
        },
        yAxis: {
          type: "category",
          data: currentData.fields,
          axisLabel: {
            color: "#E6F1FF",
            fontSize: 13,
            fontFamily: "Microsoft YaHei",
            fontWeight: "normal",
            margin: 15,
            align: "right"
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            name: "双向条形图",
            type: "bar",
            barWidth: "50%",
            itemStyle: {
              color: function(params) {
                // 根据数值正负决定颜色
                return params.value < 0 ? "#FF8B42" : "#4A90E2";
              },
              borderRadius: function(params) {
                // 根据数值正负决定圆角
                return params.value < 0 ? [4, 0, 0, 4] : [0, 4, 4, 0];
              },
              shadowColor: function(params) {
                return params.value < 0 ? 'rgba(255, 139, 66, 0.4)' : 'rgba(74, 144, 226, 0.4)';
              },
              shadowBlur: 6,
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            data: currentData.data,
            label: {
              show: false
            }
          }
        ]
      };

      this.myChart.setOption(option, true);
    },
    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.main-indicators-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;

  .chart-box {
    width: 100%;
    flex: 1;
    min-height: 280px;
    max-height: 350px;
    padding: 8px 0;
  }
}
</style>
